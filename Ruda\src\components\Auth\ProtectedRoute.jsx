import { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";

const ProtectedRoute = ({ children }) => {
  const [isValidating, setIsValidating] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const validateToken = async () => {
      const token = localStorage.getItem("token");

      if (!token) {
        setIsAuthenticated(false);
        setIsValidating(false);
        return;
      }

      // First, assume token is valid and show content immediately
      // This prevents the blank screen issue
      setIsAuthenticated(true);
      setIsValidating(false);

      // Then validate in background
      try {
        const response = await fetch(
          "https://ruda-planning.onrender.com/api/auth/profile",
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          // Token is invalid, clear localStorage and redirect
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          setIsAuthenticated(false);
          // Force redirect to login
          window.location.href = "/login";
        }
      } catch (error) {
        // Network error - keep user logged in but log the error
        console.error("Token validation error:", error);
        // Don't log out user on network errors to prevent disruption
      }
    };

    validateToken();
  }, []);

  if (isValidating) {
    // Show minimal loading screen - this should be very brief now
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          fontSize: "16px",
          backgroundColor: "#f8f9fa",
        }}
      >
        <div
          style={{
            width: "32px",
            height: "32px",
            border: "3px solid #e9ecef",
            borderTop: "3px solid #007bff",
            borderRadius: "50%",
            animation: "spin 0.8s linear infinite",
            marginBottom: "16px",
          }}
        />
        <div style={{ color: "#6c757d" }}>Loading...</div>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

export default ProtectedRoute;
