/* Style the scrollbar for webkit-based browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 6px; /* Set the width of the vertical scrollbar */
  height: 6px; /* Set the height of the horizontal scrollbar */
}

/* Style the scrollbar track (background of the scrollbar) */
::-webkit-scrollbar-track {
  background: #f1f1f1; /* Set the track color */
  border-radius: 10px;
}

/* Style the scrollbar thumb (the part that moves) */
::-webkit-scrollbar-thumb {
  background: #888; /* Set the thumb color */
  border-radius: 10px; /* Round the thumb corners */
}

/* Style the thumb when hovered */
::-webkit-scrollbar-thumb:hover {
  background: #555; /* Change color when the user hovers over the thumb */
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
/* Thin scrollbar globally */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #aaa;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: #f0f0f0;
}

@keyframes bounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
