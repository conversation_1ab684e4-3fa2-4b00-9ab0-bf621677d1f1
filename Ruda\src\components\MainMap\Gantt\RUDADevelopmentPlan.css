.ruda-container {
  width: 100%;
  height: 100vh;
  font-family: Arial, sans-serif;
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.ruda-content {
  flex: 1;
  overflow: auto;
}

.ruda-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1e3a5f;
  color: white;
  padding: 12px 20px;
  margin-bottom: 0;
}

.ruda-title {
  font-weight: bold;
  font-size: 20px;
  margin: 0;
}

.ruda-logo {
  color: #c0c0c0;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.ruda-table {
  border-collapse: collapse;
  width: 100%;
  min-width: 1600px;
  border-radius: 0px;
  overflow: hidden;
}

.ruda-header {
  background: linear-gradient(135deg, #1e3a5f 0%, #2c4a6b 100%);
  color: white;
  font-weight: bold;
  font-size: 13px;
  padding: 12px 8px;
  border: 1px solid #2c4a6b;
  text-align: center;
  vertical-align: middle;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.ruda-header.phases-packages { width: 365px; min-width: 365px; text-align: left; padding-left: 16px; }
.ruda-header.amount-column { width: 80px; min-width: 80px; }
.ruda-header.duration-column { width: 80px; min-width: 80px; }
.ruda-header.schedule-column { width: 80px; min-width: 80px; }
.ruda-header.performance-column { width: 80px; min-width: 80px; }
.ruda-header.planned-value-column { width: 80px; min-width: 80px; }
.ruda-header.earned-value-column { width: 80px; min-width: 80px; }
.ruda-header.actual-start-column { width: 80px; min-width: 80px; }
.ruda-header.actual-finish-column { width: 80px; min-width: 80px; }

.ruda-month-header {
  background-color: #1e3a5f;
  color: white;
  font-size: 9px;
  padding: 4px 1px;
  border: 1px solid #2c4a6b;
  text-align: center;
}

.ruda-phase-header {
  background: linear-gradient(135deg, #4a4a4a 0%, #5a5a5a 100%);
  color: white;
  font-weight: bold;
  font-size: 14px;
  padding: 10px 12px;
  border: 1px solid #5a5a5a;
  text-align: left;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.ruda-phase-row {
  cursor: pointer;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  transition: all 0.3s ease;
}
.ruda-phase-row:hover {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.ruda-package-row {
  cursor: pointer;
  background: linear-gradient(135deg, #e8f4fd 0%, #f1f8ff 100%);
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}
.ruda-package-row:hover {
  background: linear-gradient(135deg, #d4edda 0%, #e8f4fd 100%);
  border-left: 4px solid #2980b9;
  transform: translateX(2px);
}

.ruda-subpackage-row {
  cursor: pointer;
  background: linear-gradient(135deg, #fff8e1 0%, #fffbf0 100%);
  border-left: 4px solid #f39c12;
  transition: all 0.3s ease;
}
.ruda-subpackage-row:hover {
  background: linear-gradient(135deg, #ffeaa7 0%, #fff8e1 100%);
  border-left: 4px solid #e67e22;
  transform: translateX(2px);
}

.ruda-activity-row {
  cursor: pointer;
  background: linear-gradient(135deg, #e8f6f3 0%, #f0f9ff 100%);
  border-left: 3px solid #17a2b8;
  transition: all 0.3s ease;
}
.ruda-activity-row:hover {
  background: linear-gradient(135deg, #bee5eb 0%, #e8f6f3 100%);
  border-left: 3px solid #138496;
  transform: translateX(1px);
}

.package-cell { padding-left: 20px; font-weight: bold; color: #2c5282; font-size: 13px; }
.subpackage-cell { padding-left: 36px; font-weight: bold; color: #d36c2c; font-size: 12px; }

.ruda-subsubpackage-row {
  cursor: pointer;
  background: linear-gradient(135deg, #f0f8ff 0%, #f8fbff 100%);
  border-left: 3px solid #6c757d;
  transition: all 0.3s ease;
}
.ruda-subsubpackage-row:hover {
  background: linear-gradient(135deg, #b3e5fc 0%, #f0f8ff 100%);
  border-left: 3px solid #495057;
  transform: translateX(1px);
}

.ruda-reach-row {
  cursor: pointer;
  background: linear-gradient(135deg, #fdf2f8 0%, #fef7ff 100%);
  border-left: 3px solid #e83e8c;
  transition: all 0.3s ease;
}
.ruda-reach-row:hover {
  background: linear-gradient(135deg, #e1bee7 0%, #fdf2f8 100%);
  border-left: 3px solid #d91a72;
  transform: translateX(2px);
}

.ruda-material-row {
  cursor: pointer;
  background: linear-gradient(135deg, #f0fff4 0%, #f7fffa 100%);
  border-left: 3px solid #28a745;
  transition: all 0.3s ease;
}
.ruda-material-row:hover {
  background: linear-gradient(135deg, #c8e6c9 0%, #f0fff4 100%);
  border-left: 3px solid #1e7e34;
  transform: translateX(1px);
}

.activity-cell { padding-left: 52px; color: #1c17aa; font-size: 11px; font-weight: 500; }

.subsubpackage-cell { padding-left: 52px; font-weight: bold; color: #0253bd; font-size: 12px; }
.reach-cell { padding-left: 68px; font-weight: bold; color: #7b1fa2; font-size: 11px; }
.material-cell { padding-left: 84px; color: #2e7d32; font-size: 10px; font-weight: 500; }

.ruda-separator-row { background-color: #e2e8f0; }
.ruda-separator-cell {
  padding: 8px 16px;
  font-weight: bold;
  color: #4a5568;
  border: 1px solid #cbd5e0;
}

.ruda-cell {
  padding: 6px 10px;
  font-size: 11px;
  border: 1px solid #e0e0e0;
  background-color: white;
  text-align: left;
  transition: all 0.2s ease;
}
.ruda-cell:hover { background-color: #f8f9fa; border-color: #ced4da; }

.ruda-bold { font-weight: bold; }

.ruda-timeline-cell {
  position: relative;
  height: 24px;
  border: 1px solid #e0e0e0;
  background-color: #fafafa;
  transition: all 0.2s ease;
}
.ruda-timeline-cell:hover { background-color: #f0f0f0; border-color: #ced4da; }

.ruda-bar {
  position: absolute;
  height: 16px;
  background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  border-radius: 3px;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}
.ruda-bar:hover {
  background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.ruda-total-cell {
  background-color: #1e3a5f;
  color: white;
  font-weight: bold;
  font-size: 13px;
  padding: 8px 6px;
  border: 1px solid #2c4a6b;
  text-align: center;
}

.ruda-selected-info {
  background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
  border: 2px solid #4caf50;
  padding: 20px;
  margin: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}
.ruda-selected-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}
.ruda-selected-info h3 {
  margin: 0 0 12px 0;
  color: #1e3a5f;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
.ruda-selected-info p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}
.ruda-selected-info strong { color: #2c3e50; font-weight: 600; }

.right { text-align: right; }
.indent { padding-left: 12px; }

/* Responsive */
@media (max-width: 1200px) {
  .ruda-container { font-size: 10px; }
  .ruda-header.phases-packages { width: 300px; min-width: 300px; }
  .ruda-header.amount-column,
  .ruda-header.duration-column,
  .ruda-header.schedule-column,
  .ruda-header.performance-column { width: 80px; min-width: 80px; }
}
@media (max-width: 768px) {
  .ruda-container { font-size: 9px; }
  .ruda-table { min-width: 1200px; }
}

/* Print */
@media print {
  .ruda-container { font-size: 8px; }
  .ruda-table { min-width: auto; }
  .ruda-selected-info { display: none; }
}
