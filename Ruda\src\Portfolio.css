/* Style the scrollbar for webkit-based browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 6px; /* Set the width of the vertical scrollbar */
    height: 6px; /* Set the height of the horizontal scrollbar */
  }
  
  /* Style the scrollbar track (background of the scrollbar) */
  ::-webkit-scrollbar-track {
    background: #f1f1f1; /* Set the track color */
    border-radius: 10px;
  }
  
  /* Style the scrollbar thumb (the part that moves) */
  ::-webkit-scrollbar-thumb {
    background: #888; /* Set the thumb color */
    border-radius: 10px; /* Round the thumb corners */
  }
  
  /* Style the thumb when hovered */
  ::-webkit-scrollbar-thumb:hover {
    background: #555; /* Change color when the user hovers over the thumb */
  }
  